package com.besall.allbase.bluetooth.scan;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Build;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.TextView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bes.bessdk.connect.BleConnector;
import com.bes.bessdk.connect.SppConnector;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.scan.ScanManager;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.adapter.DeviceAdapter;
import com.besall.allbase.common.manager.PermissionManager;
import com.besall.allbase.view.base.BaseActivity;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * 蓝牙设备扫描Activity
 *
 * 该Activity提供了完整的蓝牙设备扫描功能，支持多种协议和扫描模式。
 * 主要用于发现和选择附近的蓝牙设备，为后续的连接和OTA升级做准备。
 *
 * 支持的协议：
 * - BLE (Bluetooth Low Energy): 低功耗蓝牙协议
 * - SPP (Serial Port Profile): 串口协议
 * - USB: USB连接协议
 *
 * 主要功能：
 * 1. 多协议设备扫描和发现
 * 2. 设备列表显示和管理
 * 3. 单选和多选设备支持
 * 4. 已连接设备状态显示
 * 5. 下拉刷新重新扫描
 * 6. 设备选择结果返回
 *
 * 使用场景：
 * - OTA升级前的设备选择
 * - 蓝牙设备连接前的扫描
 * - 设备管理和状态查看
 * - 多设备批量操作
 *
 * 工作流程：
 * 1. 根据Intent参数确定扫描协议
 * 2. 初始化UI界面和适配器
 * 3. 开始扫描并显示发现的设备
 * 4. 用户选择设备后返回结果
 *
 * 调用示例：
 * ```java
 * Intent intent = new Intent();
 * intent.putExtra(BluetoothConstants.Scan.BES_SCAN, BluetoothConstants.Scan.SCAN_SPP);
 * ActivityUtils.gotoActForResult(intent, REQUEST_CODE_SCAN, this, ScanActivity.class);
 * ```
 */
public class ScanActivity extends BaseActivity<IScanActivity, ScanPresenter> implements IScanActivity, SwipeRefreshLayout.OnRefreshListener, AdapterView.OnItemClickListener, ScanManager.ScanListener {

    /** 日志标签，用于调试输出 */
    private final String TAG = getClass().getSimpleName();

    /** Activity实例，用于静态访问 */
    private static ScanActivity instance;

    /** 设备协议类型，决定扫描的设备类型 */
    public DeviceProtocol deviceProtocol;

    /** 是否支持多设备选择模式 */
    private boolean isMultipleDevices = false;

    /** 设备列表视图，显示扫描到的设备 */
    private ListView mDevices;

    /** 下拉刷新控件，支持重新扫描 */
    SwipeRefreshLayout mSwipeRefresh;

    /** 设备列表适配器，管理设备数据和显示 */
    private DeviceAdapter mAdapter;

    /** 菜单对象，用于多选模式的菜单控制 */
    private Menu mMenu;

    /**
     * 创建Presenter对象
     *
     * 在MVP架构中，Presenter负责处理业务逻辑和数据操作。
     * ScanPresenter主要负责设备扫描的具体实现。
     *
     * @return ScanPresenter实例
     */
    @Override
    protected ScanPresenter createPresenter() {
        return new ScanPresenter();
    }

    /**
     * 内容设置前的初始化
     *
     * 在设置布局内容之前，解析Intent参数并确定扫描模式和协议类型。
     * 该方法会根据传入的参数配置扫描行为。
     *
     * Intent参数说明：
     * - BES_SCAN_IS_MULTIPLE_DEVICES: 是否支持多设备选择
     * - BES_SCAN: 扫描协议类型（BLE/SPP/USB）
     */
    @Override
    protected void initBeforeSetContent() {
        Intent receiveIntent = getIntent();
        // 获取是否支持多设备选择的参数
        isMultipleDevices = receiveIntent.getBooleanExtra(BluetoothConstants.Scan.BES_SCAN_IS_MULTIPLE_DEVICES, false);

        // 根据Intent参数确定扫描协议类型
        if (receiveIntent.getIntExtra(BluetoothConstants.Scan.BES_SCAN, 0) == BluetoothConstants.Scan.SCAN_BLE) {
            // BLE低功耗蓝牙协议
            deviceProtocol = DeviceProtocol.PROTOCOL_BLE;
        } else if (receiveIntent.getIntExtra(BluetoothConstants.Scan.BES_SCAN, 0) == BluetoothConstants.Scan.SCAN_SPP) {
            // SPP串口协议
            deviceProtocol = DeviceProtocol.PROTOCOL_SPP;
        } else if (receiveIntent.getIntExtra(BluetoothConstants.Scan.BES_SCAN, 0) == BluetoothConstants.Scan.SCAN_USB) {
            // USB连接协议（不支持多设备选择）
            isMultipleDevices = false;
            deviceProtocol = DeviceProtocol.PROTOCOL_USB;
        } else {
            // 无效的协议参数，直接返回
           return;
        }
    }

    /**
     * 获取布局文件ID
     *
     * @return 扫描Activity的布局文件ID
     */
    @Override
    protected int getContentViewId() {
        return R.layout.act_scan;
    }

    /**
     * 绑定视图控件
     *
     * 初始化所有UI控件并建立引用关系。创建设备适配器并绑定到ListView。
     */
    @Override
    protected void bindView() {
        // 创建设备适配器，传入协议类型用于显示不同的设备信息
        mAdapter = new DeviceAdapter(this, deviceProtocol);
        // 绑定设备列表视图
        mDevices = (ListView)findViewById(R.id.devices);
        // 绑定下拉刷新控件
        mSwipeRefresh = (SwipeRefreshLayout)findViewById(R.id.swipe_refresh);
        // 绑定标题文本视图
        tv_title = (TextView)findViewById(R.id.tv_title);
    }

    /**
     * 初始化视图和设置监听器
     *
     * 配置UI控件的行为，设置标题文本，启动设备扫描，并根据Android版本
     * 请求必要的权限。
     */
    @Override
    protected void initView() {
        // 设置适配器的多设备选择模式
        mAdapter.setIsMultipleDevices(isMultipleDevices);
        // 将适配器绑定到ListView
        mDevices.setAdapter(mAdapter);
        // 设置列表项点击监听器
        mDevices.setOnItemClickListener(this);
        // 设置下拉刷新监听器
        mSwipeRefresh.setOnRefreshListener(this);
        // 初始化工具栏
        inittoolbar("");

        // 根据协议类型设置不同的标题文本
        if (deviceProtocol == DeviceProtocol.PROTOCOL_BLE) {
            tv_title.setText(R.string.activity_bluetooth_scan);
        } else if (deviceProtocol == DeviceProtocol.PROTOCOL_SPP) {
            tv_title.setText(R.string.activity_classics_devices_scan);
        } else if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
            tv_title.setText(R.string.activity_USB_scan);
        }

        // 开始设备扫描
        startScan();

        // 根据Android版本请求存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12及以上版本的权限处理
        } else {
            // Android 12以下版本需要请求外部存储写入权限
            PermissionManager.getInstance().requestPermissions(this, null, PermissionManager.Permission.Storage.WRITE_EXTERNAL_STORAGE);
        }
    }

    /**
     * 设置Activity实例
     *
     * 保存当前Activity实例的静态引用，用于在其他地方访问。
     */
    @Override
    protected void setInstance() {
        instance = this;
    }

    /**
     * 移除Activity实例
     *
     * 清除静态实例引用，防止内存泄漏。
     */
    @Override
    protected void removeInstance() {
        instance = null;
    }

    /**
     * Activity销毁时的清理工作
     *
     * 在Activity销毁时停止设备扫描，释放相关资源。
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 停止设备扫描，避免资源泄漏
        stopScan();
    }

    /**
     * 处理菜单项选择事件
     *
     * 处理工具栏菜单项的点击事件，包括返回按钮和选择确认按钮。
     * 在多设备选择模式下，会返回选中的设备列表。
     *
     * @param item 被选择的菜单项
     * @return 是否处理了该事件
     */
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                // 处理返回按钮点击
                Log.i(TAG, "onOptionsItemSelected: -------------");
                stopScan();
                // 如果是多设备选择模式且有选中的设备，返回选中结果
                if (deviceProtocol != DeviceProtocol.PROTOCOL_USB && mAdapter.getSelectedDevices().size() > 0 && isMultipleDevices) {
                    Intent intent = new Intent();
                    intent.putExtra(BluetoothConstants.Scan.BES_SCAN_RESULT, (Serializable) mAdapter.getSelectedDevices());
                    setResult(RESULT_OK, intent);
                }
                finish();
                break;
            case R.id.menu_select:
                // 处理选择确认按钮点击
                if (mAdapter.getSelectedDevices().size() > 0 && isMultipleDevices) {
                    stopScan();
                    Intent intent = new Intent();
                    intent.putExtra(BluetoothConstants.Scan.BES_SCAN_RESULT, (Serializable) mAdapter.getSelectedDevices());
                    setResult(RESULT_OK, intent);
                    finish();
                }
                break;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 创建选项菜单
     *
     * 在多设备选择模式下创建选择确认菜单，单设备模式下不显示菜单。
     *
     * @param menu 菜单对象
     * @return 是否创建了菜单
     */
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (isMultipleDevices) {
            // 多设备选择模式下加载选择菜单
            getMenuInflater().inflate(R.menu.menu_select, menu);
            mMenu = menu;
        }
        return true;
    }

    /**
     * 处理列表项点击事件
     *
     * 根据不同的协议类型和选择模式处理设备选择逻辑：
     * - USB协议：直接返回选中设备
     * - 多设备模式：切换选择状态并更新菜单图标
     * - 单设备模式：直接返回选中设备并结束Activity
     *
     * @param parent 父视图
     * @param view 被点击的视图
     * @param position 点击位置
     * @param id 项目ID
     */
    @SuppressLint("RestrictedApi")
    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
            // USB协议直接返回选中设备
            Intent intent = new Intent();
            intent.putExtra(BluetoothConstants.Scan.BES_SCAN_RESULT, mAdapter.getItem(position));
            setResult(RESULT_OK, intent);
            finish();
            return;
        }

        if (isMultipleDevices) {
            // 多设备选择模式：切换选择状态
            mAdapter.addSelectState(position);
            int size = mAdapter.getSelectedDevices().size();
            MenuItem item = mMenu.findItem(R.id.menu_select);
            // 根据选中设备数量更新菜单图标（普通状态/选中状态）
            item.setIcon(size == 0 ? R.drawable.ota_top_nor : R.drawable.ota_top_sele);
        } else {
            // 单设备选择模式：直接返回选中设备
            mAdapter.addSelectState(position);
            Intent intent = new Intent();
            intent.putExtra(BluetoothConstants.Scan.BES_SCAN_RESULT, mAdapter.getItem(position));
            setResult(RESULT_OK, intent);
            finish();
        }
    }

    /**
     * 处理下拉刷新事件
     *
     * 当用户下拉刷新时，清空当前设备列表并重新开始扫描。
     * 在多设备模式下还会重置菜单图标状态。
     */
    @Override
    public void onRefresh() {
        if (mAdapter != null) {
            // 清空设备列表
            mAdapter.clear();
            if (isMultipleDevices) {
                // 重置选择菜单图标为普通状态
                MenuItem item = mMenu.findItem(R.id.menu_select);
                item.setIcon(R.drawable.ota_top_nor);
            }
        }
        // 重新开始扫描
        startScan();
    }

    /**
     * 扫描结果回调
     *
     * 当扫描到新设备时会调用此方法。将扫描到的设备添加到设备列表中。
     *
     * @param scannedDevice 扫描到的设备对象
     */
    @Override
    public void onScanResult(HmDevice scannedDevice) {
        addDevice(scannedDevice, false);
    }

    /**
     * 添加设备到列表
     *
     * 将设备添加到适配器中，支持添加到列表前端或后端。
     * 已连接的设备通常添加到前端，新扫描的设备添加到后端。
     *
     * @param hmDevice 要添加的设备对象
     * @param before 是否添加到列表前端，true表示添加到前端，false表示添加到后端
     */
    private void addDevice(HmDevice hmDevice, boolean before) {
        if (before) {
            // 添加到列表前端（通常用于已连接设备）
            mAdapter.addBefore(hmDevice, hmDevice.getRssi());
        } else {
            // 添加到列表后端（通常用于新扫描设备）
            mAdapter.add(hmDevice, hmDevice.getRssi());
        }
    }

    /**
     * 设备离线回调
     *
     * 当已连接的设备离线时会调用此方法。当前实现为空，
     * 可根据需要添加设备离线处理逻辑。
     *
     * @param device 离线的设备对象
     */
    @Override
    public void onDeviceOffline(HmDevice device) {
        // 当前为空实现，可根据需要添加离线处理逻辑
    }

    /**
     * 扫描失败回调
     *
     * 当设备扫描失败时会调用此方法。当前实现是重新开始扫描，
     * 以确保扫描过程的连续性。
     *
     * @param message 失败消息
     */
    @Override
    public void onScanFailed(String message) {
        // 扫描失败时重新开始扫描
        startScan();
    }

    /**
     * 开始设备扫描
     *
     * 启动设备扫描过程，包括以下步骤：
     * 1. 清空当前设备列表
     * 2. 添加已连接的设备到列表前端
     * 3. 开始新的设备扫描
     *
     * 对于USB协议，直接开始扫描；对于蓝牙协议，会先添加已连接设备。
     */
    private void startScan() {
        if (mAdapter != null) {
            // 清空当前设备列表
            mAdapter.clear();
        }

        if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
            // USB协议直接开始扫描
            mPresenter.starScanWithScanType(instance, deviceProtocol);
            return;
        }

        // 获取当前已连接的SPP设备并添加到列表前端
        ArrayList<HmDevice> curConSppDevices = SppConnector.getsConnector(instance, null).getCurConnectDevices();
        for (int i = 0; i < curConSppDevices.size(); i ++) {
            HmDevice hmDevice = curConSppDevices.get(i);
            // 设置高RSSI值以确保已连接设备显示在列表前端
            hmDevice.setRssi(1000);
            addDevice(hmDevice, true);
        }

        // 获取当前已连接的BLE设备并添加到列表前端
        ArrayList<HmDevice> curConBleDevices = BleConnector.getsConnector(instance, null, null).getCurConnectDevices();
        for (int i = 0; i < curConBleDevices.size(); i ++) {
            HmDevice hmDevice = curConBleDevices.get(i);
            // 设置高RSSI值以确保已连接设备显示在列表前端
            hmDevice.setRssi(1000);
            addDevice(hmDevice, true);
        }

        // 开始新的设备扫描
        mPresenter.starScanWithScanType(instance, deviceProtocol);
    }

    /**
     * 停止设备扫描
     *
     * 停止当前正在进行的设备扫描，释放扫描相关资源。
     * 通常在Activity销毁或用户退出时调用。
     */
    private void stopScan() {
        mPresenter.stopScanWithScanType(instance, deviceProtocol);
    }
}
