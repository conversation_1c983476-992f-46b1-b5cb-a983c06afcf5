# BesOTA Android 项目文件结构

## 完整项目结构树

```
BesOTA_android/
│
├── 📁 app/                                    # 主应用模块
│   ├── 📁 build/                              # 构建输出目录
│   ├── 📁 jniLibs/                            # JNI原生库
│   ├── 📁 src/
│   │   ├── 📁 androidTest/                    # Android测试
│   │   ├── 📁 main/
│   │   │   ├── 📄 AndroidManifest.xml         # 应用清单文件
│   │   │   ├── 📁 assets/                     # 资源文件
│   │   │   ├── 📁 java/com/besall/allbase/
│   │   │   │   ├── 📁 app/                    # 应用程序入口
│   │   │   │   ├── 📁 bluetooth/              # 蓝牙功能模块
│   │   │   │   │   ├── 📄 BluetoothConstants.java
│   │   │   │   │   └── 📁 scan/               # 蓝牙扫描
│   │   │   │   │       └── 📄 ScanActivity.java
│   │   │   │   ├── 📁 common/                 # 通用工具类
│   │   │   │   │   └── 📁 utils/              # 工具类集合
│   │   │   │   │       ├── 📄 ActivityUtils.java
│   │   │   │   │       └── 📄 FileUtils.java
│   │   │   │   └── 📁 view/                   # UI视图层
│   │   │   │       ├── 📄 BesOtaUtils.kt      # OTA工具类
│   │   │   │       ├── 📄 BTBondUtils.kt      # 蓝牙绑定工具
│   │   │   │       ├── 📄 CrcUtil.java        # CRC校验工具
│   │   │   │       ├── 📁 activity/           # Activity集合
│   │   │   │       │   ├── 📄 HomeActivity.java        # 主页Activity
│   │   │   │       │   ├── 📄 TestActivity.java        # 测试Activity
│   │   │   │       │   ├── 📁 chipstoollevel4/         # 芯片工具四级功能
│   │   │   │       │   │   ├── 📁 checkcrc/            # CRC校验功能
│   │   │   │       │   │   │   ├── 📄 CheckCrcActivity.java
│   │   │   │       │   │   │   ├── 📄 CheckCrcPresenter.java
│   │   │   │       │   │   │   ├── 📄 ICheckCrcActivity.java
│   │   │   │       │   │   │   └── 📄 ICheckCrcPresenter.java
│   │   │   │       │   │   ├── 📁 commandset/          # 命令集功能
│   │   │   │       │   │   │   ├── 📄 CommandSetActivity.java
│   │   │   │       │   │   │   ├── 📄 CommandSetPresenter.java
│   │   │   │       │   │   │   ├── 📄 MyScrollView.java
│   │   │   │       │   │   │   └── 📄 MyScrollViewListener.java
│   │   │   │       │   │   ├── 📁 customercmd/         # 自定义命令
│   │   │   │       │   │   │   ├── 📄 CustomCmdActivity.java
│   │   │   │       │   │   │   ├── 📄 CustomCmdPresenter.java
│   │   │   │       │   │   │   ├── 📄 DeleteaccountDialog.java
│   │   │   │       │   │   │   └── 📄 SpinerPopWindow.java
│   │   │   │       │   │   ├── 📁 customerdial/        # 自定义表盘
│   │   │   │       │   │   │   ├── 📄 CustomerDialActivity.java
│   │   │   │       │   │   │   ├── 📄 CustomerDialBean.java
│   │   │   │       │   │   │   ├── 📄 CustomerDialPresenter.java
│   │   │   │       │   │   │   └── 📁 makedial/        # 表盘制作
│   │   │   │       │   │   ├── 📁 findmy/              # 查找设备功能
│   │   │   │       │   │   │   ├── 📄 FindMyActivity.java
│   │   │   │       │   │   │   ├── 📄 FindMyPresenter.java
│   │   │   │       │   │   │   ├── 📄 IFindMyActivity.java
│   │   │   │       │   │   │   └── 📄 IFindMyPresenter.java
│   │   │   │       │   │   └── 📁 ota/                 # ⭐ OTA升级功能 ⭐
│   │   │   │       │   │       ├── 📄 DemoActivity.java         # 🎯 OTA演示Activity
│   │   │   │       │   │       ├── 📄 OtaUIActivity.java        # 完整OTA界面
│   │   │   │       │   │       ├── 📄 OtaUIPresenter.java       # OTA业务逻辑
│   │   │   │       │   │       ├── 📄 IOtaUIActivity.java       # OTA接口定义
│   │   │   │       │   │       ├── 📄 IOtaUIPresenter.java      # OTA Presenter接口
│   │   │   │       │   │       ├── 📄 OtaConfigFragment.java    # OTA配置片段
│   │   │   │       │   │       ├── 📁 filepathadapter/          # 文件路径适配器
│   │   │   │       │   │       └── 📁 otafilelist/             # OTA文件列表
│   │   │   │       │   ├── 📁 level2/              # 二级功能
│   │   │   │       │   ├── 📁 level3/              # 三级功能
│   │   │   │       │   └── 📁 tools/               # 工具集
│   │   │   │       └── 📁 base/                    # 基础类
│   │   │   │           ├── 📄 BaseActivity.java    # Activity基类
│   │   │   │           └── 📄 BasePresenter.java   # Presenter基类
│   │   │   └── 📁 res/                             # 资源文件
│   │   │       ├── 📁 anim/                        # 动画资源
│   │   │       ├── 📁 drawable/                    # 图片资源
│   │   │       │   ├── 📁 drawable-hdpi/           # 高密度图片
│   │   │       │   ├── 📁 drawable-mdpi/           # 中密度图片
│   │   │       │   ├── 📁 drawable-xhdpi/          # 超高密度图片
│   │   │       │   ├── 📁 drawable-xxhdpi/         # 超超高密度图片
│   │   │       │   └── 📁 drawable-xxxhdpi/        # 超超超高密度图片
│   │   │       ├── 📁 font/                        # 字体资源
│   │   │       ├── 📁 layout/                      # 布局文件
│   │   │       │   ├── 📄 activity_demo.xml        # 🎯 DemoActivity布局
│   │   │       │   ├── 📄 activity_main.xml        # 主页布局
│   │   │       │   ├── 📄 act_ota.xml              # OTA布局
│   │   │       │   └── 📄 ... (其他布局文件)
│   │   │       ├── 📁 menu/                        # 菜单资源
│   │   │       ├── 📁 mipmap/                      # 应用图标
│   │   │       ├── 📁 raw/                         # 原始资源文件
│   │   │       ├── 📁 values/                      # 值资源
│   │   │       │   ├── 📄 colors.xml               # 颜色定义
│   │   │       │   ├── 📄 strings.xml              # 字符串资源
│   │   │       │   ├── 📄 styles.xml               # 样式定义
│   │   │       │   └── 📄 dimens.xml               # 尺寸定义
│   │   │       ├── 📁 values-zh-rCN/               # 中文资源
│   │   │       └── 📁 xml/                         # XML配置文件
│   │   └── 📁 test/                                # 单元测试
│   ├── 📄 build.gradle                             # 模块构建配置
│   ├── 📄 gradle.properties                        # Gradle属性
│   └── 📄 proguard-rules.pro                       # 混淆规则
│
├── 📁 besota/                                      # BES OTA SDK模块
│   ├── 📁 build/                                   # 构建输出
│   ├── 📁 src/
│   │   ├── 📁 androidTest/                         # Android测试
│   │   ├── 📁 main/
│   │   │   ├── 📄 AndroidManifest.xml              # SDK清单
│   │   │   ├── 📁 java/                            # SDK源码
│   │   │   └── 📁 jniLibs/                         # 原生库文件
│   │   │       └── 📁 arm64-v8a/                   # ARM64架构库
│   │   └── 📁 test/                                # 单元测试
│   ├── 📄 build.gradle                             # SDK构建配置
│   ├── 📄 consumer-rules.pro                       # 消费者规则
│   └── 📄 proguard-rules.pro                       # SDK混淆规则
│
├── 📁 build/                                       # 项目构建输出
├── 📁 gradle/                                      # Gradle配置
│   └── 📁 wrapper/                                 # Gradle包装器
├── 📄 build.gradle                                 # 项目级构建配置
├── 📄 gradle.properties                            # 全局Gradle属性
├── 📄 gradlew                                      # Gradle包装器脚本(Unix)
├── 📄 gradlew.bat                                  # Gradle包装器脚本(Windows)
├── 📄 local.properties                             # 本地属性配置
├── 📄 login-with-amazon-sdk.jar                    # Amazon登录SDK
└── 📄 settings.gradle                              # 项目设置
```

## 关键文件说明

### 🎯 核心文件 - DemoActivity.java
**路径**: `app/src/main/java/com/besall/allbase/view/activity/chipstoollevel4/ota/DemoActivity.java`
**功能**: OTA升级演示界面，提供简化的OTA升级流程展示

### 📱 主要配置文件

| 文件 | 路径 | 说明 |
|------|------|------|
| AndroidManifest.xml | app/src/main/ | 应用清单，定义权限和组件 |
| build.gradle (项目级) | 根目录 | 项目级构建配置 |
| build.gradle (app) | app/ | 应用模块构建配置 |
| activity_demo.xml | app/src/main/res/layout/ | DemoActivity布局文件 |

### 📚 主要功能模块

| 模块 | 路径 | 功能描述 |
|------|------|----------|
| OTA升级 | chipstoollevel4/ota/ | 固件升级功能 |
| 蓝牙管理 | bluetooth/ | 蓝牙连接和扫描 |
| CRC校验 | chipstoollevel4/checkcrc/ | 文件校验功能 |
| 自定义命令 | chipstoollevel4/customercmd/ | 设备命令控制 |
| 表盘制作 | chipstoollevel4/customerdial/ | 自定义表盘功能 |
| 设备查找 | chipstoollevel4/findmy/ | 查找设备功能 |

### 🔧 开发工具文件

| 文件类型 | 说明 |
|----------|------|
| .gradle | Gradle构建系统配置 |
| .properties | 属性配置文件 |
| .pro | ProGuard混淆规则 |
| .jar | 第三方SDK库文件 |

---

*此文档展示了BesOTA Android项目的完整文件结构，重点标注了DemoActivity相关文件的位置和作用。*
