package com.bes.bessdk.service.base;

import com.bes.sdk.device.HmDevice;

/**
 * BES服务监听器接口
 *
 * 该接口定义了BES服务的核心回调方法，用于监听和处理BES设备服务过程中的各种状态变化和事件。
 * 实现此接口的类可以接收到连接状态变化、错误信息、状态变化消息和成功消息等回调通知。
 *
 * 主要功能：
 * 1. 监听Tota连接状态变化
 * 2. 处理服务过程中的错误消息
 * 3. 接收状态变化通知
 * 4. 处理操作成功的消息
 *
 * 使用场景：
 * - OTA固件升级过程监控
 * - 蓝牙设备连接状态监听
 * - 设备服务状态跟踪
 * - 错误处理和用户反馈
 *
 * 实现建议：
 * - 在UI线程中更新界面状态
 * - 根据不同的消息码进行相应处理
 * - 提供用户友好的错误提示
 * - 记录关键状态变化日志
 */
public interface BesServiceListener {

    /**
     * Tota连接状态变化回调
     *
     * 当Tota协议连接状态发生变化时会调用此方法。Tota是BES芯片的专用通信协议，
     * 用于设备间的数据传输和控制命令交互。
     *
     * 调用时机：
     * - Tota连接建立成功时
     * - Tota连接断开时
     * - 连接过程中状态变化时
     *
     * @param state 连接状态，true表示已连接，false表示已断开
     * @param hmDevice 相关的HM设备对象，包含设备信息和连接参数
     */
    void onTotaConnectState(boolean state, HmDevice hmDevice);

    /**
     * 错误消息回调
     *
     * 当BES服务过程中发生错误时会调用此方法。错误可能包括连接失败、
     * 数据传输错误、协议错误、设备响应超时等各种异常情况。
     *
     * 调用时机：
     * - 连接建立失败时
     * - 数据传输过程中出现错误
     * - 设备响应异常或超时
     * - 协议解析错误时
     *
     * 处理建议：
     * - 根据错误码显示相应的用户提示
     * - 记录错误日志便于问题排查
     * - 必要时进行重试或恢复操作
     *
     * @param msg 错误消息码，用于标识具体的错误类型
     * @param hmDevice 发生错误的HM设备对象
     */
    void onErrorMessage(int msg, HmDevice hmDevice);

    /**
     * 状态变化消息回调
     *
     * 当BES服务状态发生变化时会调用此方法。这是最重要的回调方法之一，
     * 用于通知服务过程中的各种状态变化，如连接建立、数据传输开始、
     * 升级进度更新等。
     *
     * 调用时机：
     * - 服务连接状态变化时
     * - OTA升级过程中的关键节点
     * - 设备硬件信息获取成功时
     * - 数据传输状态变化时
     *
     * 常见消息码：
     * - OTA_CMD_GET_HW_INFO: 获取硬件信息成功，可以开始数据传输
     * - BES_CONNECT_SUCCESS: BES连接建立成功
     *
     * 处理建议：
     * - 根据消息码执行相应的业务逻辑
     * - 更新UI界面显示当前状态
     * - 在关键状态节点进行日志记录
     *
     * @param msg 状态消息码，标识具体的状态变化类型
     * @param msgStr 状态消息字符串，提供额外的状态描述信息
     * @param hmDevice 相关的HM设备对象
     */
    void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice);

    /**
     * 成功消息回调
     *
     * 当BES服务过程中某个操作成功完成时会调用此方法。用于通知
     * 操作成功的消息，如连接建立成功、数据发送成功、命令执行成功等。
     *
     * 调用时机：
     * - 连接建立成功时
     * - 数据传输完成时
     * - 命令执行成功时
     * - 配置设置成功时
     *
     * 处理建议：
     * - 更新UI显示操作成功状态
     * - 执行后续的业务逻辑
     * - 记录成功操作的日志
     *
     * @param msg 成功消息码，标识具体的成功操作类型
     * @param hmDevice 相关的HM设备对象
     */
    void onSuccessMessage(int msg, HmDevice hmDevice);

}

