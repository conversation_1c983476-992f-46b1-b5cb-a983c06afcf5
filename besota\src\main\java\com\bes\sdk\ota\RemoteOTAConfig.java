package com.bes.sdk.ota;

import android.util.Log;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.Serializable;

/**
 * 远程OTA配置类
 *
 * 该类用于配置和管理远程OTA（Over-The-Air）固件升级的相关参数。
 * 包含了OTA升级过程中所需的所有配置信息，如产品标识、版本信息、
 * 下载地址、本地存储路径、文件校验和以及更新说明等。
 *
 * 主要功能：
 * 1. 存储OTA升级的配置参数
 * 2. 提供固件版本和下载信息管理
 * 3. 支持本地文件路径配置
 * 4. 提供文件完整性校验支持
 * 5. 支持多语言的更新说明
 *
 * 使用场景：
 * - 远程OTA升级配置
 * - 固件版本管理
 * - 升级文件下载和存储
 * - 升级过程的参数传递
 *
 * OTA configuration API.
 */
public class RemoteOTAConfig implements Serializable {

    /** 产品标识符，用于唯一标识远程OTA配置 */
    private String pid;

    /** 固件版本号，定义在远程配置中的版本信息 */
    private String version;

    /** OTA固件下载URL地址 */
    private String downloadUrl;

    /** 本地文件路径，ScanManager下载并存储最新固件的位置 */
    private String localPath;

    /** 主设备本地文件路径，用于主从设备架构中的主设备固件路径 */
    private String localPathMaster;

    /** 固件文件的MD5校验和，用于验证文件完整性 */
    private String checkSum;

    /** 更新说明标题，支持特定语言的本地化 */
    private String whatsNewTitle;

    /** 更新说明内容，支持特定语言的本地化 */
    private String whatsNewContent;

    /**
     * 获取产品标识符
     *
     * 产品标识符（PID）用于唯一标识远程OTA配置。每个产品都有唯一的PID，
     * 用于区分不同的设备型号和配置。
     *
     * Get PID. Remote OTA configuration is identified by PID.
     *
     * @return 产品标识符字符串，如果未设置则返回null
     */
    public String getPid() { return pid;}

    /**
     * 设置产品标识符
     *
     * 设置用于标识远程OTA配置的产品标识符。该标识符应该与服务器端
     * 配置的产品ID保持一致。
     *
     * @see #getPid()
     * @param pid 产品标识符，不能为空
     */
    public void setPid(String pid) {
        this.pid = pid;
    }

    /**
     * 获取固件版本号
     *
     * 获取在远程配置中定义的固件版本号。版本号用于判断是否需要升级，
     * 以及确定升级的目标版本。
     *
     * Get firmware version defined in remote configuration.
     *
     * @return 固件版本号字符串，如果未设置则返回null
     */
    public String getVersion() {
        return version;
    }

    /**
     * 设置固件版本号
     *
     * 设置目标固件的版本号。该版本号应该与实际固件文件的版本信息一致。
     *
     * @see #getVersion()
     * @param version 固件版本号，建议使用语义化版本格式
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * 获取OTA下载URL地址
     *
     * 获取在远程配置中定义的OTA固件下载URL地址。该URL指向固件文件的
     * 下载位置，通常是HTTP或HTTPS协议的网络地址。
     *
     * Get OTA download URL defined in remote configuration.
     *
     * @return OTA固件下载URL字符串，如果未设置则返回null
     */
    public String getDownloadUrl() {
        return downloadUrl;
    }

    /**
     * 设置OTA下载URL地址
     *
     * 设置固件文件的下载URL地址。该地址应该是可访问的网络地址，
     * 支持HTTP/HTTPS协议。
     *
     * @see #getDownloadUrl()
     * @param downloadUrl OTA固件下载URL，应该是有效的网络地址
     */
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    /**
     * 获取本地文件路径
     *
     * 获取ScanManager下载并存储最新固件的本地文件路径。该路径指向
     * 设备本地存储中固件文件的位置。
     *
     * Local path that ScanManager downloads and store the latest firmware.
     *
     * @return 本地固件文件路径字符串，如果未设置则返回null
     */
    public String getLocalPath() {
        return localPath;
    }

    /**
     * 设置本地文件路径
     *
     * 设置固件文件在本地设备中的存储路径。该路径应该是设备可访问的
     * 有效文件系统路径。
     *
     * @see #getLocalPath()
     * @param localPath 本地固件文件路径，应该是有效的文件系统路径
     */
    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    /**
     * 获取主设备本地文件路径
     *
     * 在主从设备架构中，获取主设备固件文件的本地存储路径。
     * 用于支持双芯片或主从设备的OTA升级场景。
     *
     * @return 主设备本地固件文件路径字符串，如果未设置则返回null
     */
    public String getLocalPathMaster() {
        return localPathMaster;
    }

    /**
     * 设置主设备本地文件路径
     *
     * 设置主设备固件文件在本地设备中的存储路径。该路径用于主从设备
     * 架构中的主设备固件升级。
     *
     * @see #getLocalPath()
     * @param localPathMaster 主设备本地固件文件路径
     */
    public void setLocalPathMaster(String localPathMaster) {
        this.localPathMaster = localPathMaster;
    }

    /**
     * 获取固件MD5校验和
     *
     * 获取在远程配置中定义的固件文件MD5校验和。该校验和用于验证
     * 下载的固件文件是否完整且未被篡改。
     *
     * Get firmware MD5 checksum defined in remote configuration.
     *
     * @return 固件文件的MD5校验和字符串，如果未设置则返回null
     */
    public String getCheckSum() {
        return checkSum;
    }

    /**
     * 设置固件MD5校验和
     *
     * 设置固件文件的MD5校验和，用于文件完整性验证。在OTA升级过程中，
     * 系统会使用该校验和验证下载文件的完整性。
     *
     * @see #getCheckSum()
     * @param checkSum 固件文件的MD5校验和，应该是32位十六进制字符串
     */
    public void setCheckSum(String checkSum) {
        this.checkSum = checkSum;
    }

    /**
     * 获取特定语言的更新说明标题
     *
     * 获取针对特定语言环境的"新功能"标题。该标题用于向用户展示
     * 本次固件更新的主要特性说明。
     *
     * Get What's New title for specific language.
     *
     * @param locale 语言环境标识符（当前版本中该参数未使用）
     * @return 更新说明标题字符串，如果未设置则返回null
     */
    public String getWhatsNewTitle(String locale) {
        return whatsNewTitle;
    }

    /**
     * 设置更新说明标题
     *
     * 设置固件更新的"新功能"标题，用于向用户展示更新的主要内容概述。
     *
     * @see #getWhatsNewTitle(String)
     * @param whatsNewTitle 更新说明标题，建议简洁明了
     */
    public void setWhatsNewTitle(String whatsNewTitle) {
        this.whatsNewTitle = whatsNewTitle;
    }

    /**
     * 获取特定语言的更新说明内容
     *
     * 获取针对特定语言环境的"新功能"详细内容。该内容用于向用户详细
     * 说明本次固件更新包含的新功能、改进和修复。
     *
     * Get What's New content for specific language.
     *
     * @param locale 语言环境标识符（当前版本中该参数未使用）
     * @return 更新说明详细内容字符串，如果未设置则返回null
     */
    public String getWhatsNewContent(String locale) {
        return whatsNewContent;
    }

    /**
     * 设置更新说明内容
     *
     * 设置固件更新的详细说明内容，用于向用户详细介绍本次更新的
     * 新功能、改进项和问题修复等信息。
     *
     * @see #getWhatsNewContent(String)
     * @param whatsNewContent 更新说明详细内容，可以包含多行文本
     */
    public void setWhatsNewContent(String whatsNewContent) {
        this.whatsNewContent = whatsNewContent;
    }

    /**
     * 返回对象的字符串表示
     *
     * 生成包含所有配置参数的字符串表示，主要用于调试和日志输出。
     * 该方法会输出所有重要的配置信息，便于开发者查看当前配置状态。
     *
     * @return 包含所有配置参数的格式化字符串
     */
    @Override
    public String toString() {
        return "RemoteOTAConfig{" +
                "pid='" + pid + '\'' +
                ", version='" + version + '\'' +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", localPath='" + localPath + '\'' +
                ", localPathMaster='" + localPathMaster + '\'' +
                ", checkSum='" + checkSum + '\'' +
                ", whatsNewTitle='" + whatsNewTitle + '\'' +
                ", whatsNewContent='" + whatsNewContent + '\'' +
                '}';
    }
}
