package com.besall.allbase.view.activity.chipstoollevel4.ota;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.bes.bessdk.service.BesOTAConstants.OTA_CMD_GET_HW_INFO;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;

import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.format.DateFormat;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.BesOtaService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.LogUtils;
import com.bes.bessdk.utils.SPHelper;
import com.bes.d3dsdk.ImageMagic;
import com.bes.sdk.core.BesOtaCallback;
import com.bes.sdk.core.BesOtaEntity;
import com.bes.sdk.core.BesOtaManager;
import com.bes.sdk.core.OtaLog;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.ota.OTADfuInfo;
import com.bes.sdk.ota.OTATask;
import com.bes.sdk.ota.RemoteOTAConfig;
import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.utils.OTAStatus;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.BesOtaUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * BES OTA固件升级演示Activity
 *
 * 该Activity提供了一个完整的OTA（Over-The-Air）固件升级演示界面，主要功能包括：
 * 1. 蓝牙设备扫描和选择
 * 2. OTA升级文件路径配置
 * 3. OTA升级任务的启动和管理
 * 4. 升级进度监控和状态显示
 * 5. 升级过程中的错误处理和状态回调
 *
 * 支持的协议：
 * - SPP (Serial Port Profile) 协议
 * - BLE (Bluetooth Low Energy) 协议
 *
 * 使用流程：
 * 1. 点击设备选择按钮，扫描并选择目标蓝牙设备
 * 2. 确认升级文件路径（默认路径可在代码中修改）
 * 3. 点击开始升级按钮，建立连接并开始OTA升级
 * 4. 监控升级进度和状态变化
 *
 * <AUTHOR>
 * @date 2023/6/21 17:28
 */
public class DemoActivity extends AppCompatActivity implements BesServiceListener, OTATask.StatusListener {

    /** 选中的蓝牙设备对象 */
    BluetoothDevice mDevice;

    /** HM设备对象，包含设备的详细信息和协议配置 */
    HmDevice mHmDevice;

    /** OTA升级任务对象，负责执行具体的升级操作 */
    OTATask otaTask;

    /** BES服务配置对象，用于配置连接参数和协议设置 */
    BesServiceConfig mServiceConfig;

    /** 设备选择按钮视图 */
    View btDeviceSelect;

    /** 显示选中设备信息的文本视图 */
    TextView tvDevice;

    /** 显示升级文件路径的文本视图 */
    TextView tvFileSelect;

    /** OTA升级开始按钮视图 */
    View tvOtaUpdate;

    /** 显示升级状态的文本视图 */
    TextView tvState;

    /** 显示升级日志和进度的文本视图 */
    TextView tvLog;

    /**
     * Activity创建时的初始化方法
     *
     * 主要完成以下初始化工作：
     * 1. 设置布局文件
     * 2. 初始化所有UI控件
     * 3. 设置默认的OTA升级文件路径
     * 4. 设置各种点击事件监听器
     * 5. 记录Activity创建日志
     *
     * @param savedInstanceState 保存的实例状态
     */
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置Activity的布局文件
        setContentView(R.layout.activity_demo);

        // 初始化UI控件
        btDeviceSelect = findViewById(R.id.btDeviceSelect);  // 设备选择按钮
        tvDevice = findViewById(R.id.tvDevice);              // 设备信息显示
        tvFileSelect = findViewById(R.id.tvFileSelect);      // 文件路径显示

        // ⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️ 重要：请将此路径修改为您的实际文件路径
        // 设置默认的OTA升级文件路径，用户需要根据实际情况修改此路径
        tvFileSelect.setText("/storage/emulated/0/Android/data/com.bes.besall/test.bin");
        // 备用路径示例：/storage/emulated/0/Android/data/com.bes.besall/files/bin/test.bin

        tvOtaUpdate = findViewById(R.id.tvOtaUpdate);        // OTA升级开始按钮
        tvState = findViewById(R.id.tvState);                // 状态显示
        tvLog = findViewById(R.id.tvLog);                    // 日志显示

        // 记录Activity创建日志
        OtaLog.logI("DemoActivity onCreate");

        // 设备选择按钮点击事件监听器
        // 点击后会启动设备扫描Activity，用于选择要进行OTA升级的蓝牙设备
        btDeviceSelect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                // 设置扫描类型为SPP协议设备
                intent.putExtra(BluetoothConstants.Scan.BES_SCAN, BluetoothConstants.Scan.SCAN_SPP);
                // 启动设备扫描Activity并等待结果返回
                ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, DemoActivity.this, ScanActivity.class);
            }
        });

        // OTA升级开始按钮点击事件监听器
        // 点击后会初始化OTA服务配置并准备开始升级流程
        tvOtaUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // ⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️ 重要：必须先初始化ServiceConfig
                // 创建BES服务配置对象
                mServiceConfig = new BesServiceConfig();

                // BLE协议配置示例（已注释）
                // 如需使用BLE协议，请取消以下注释并注释SPP相关配置
//                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
//                mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
//                mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_OTA_CHARACTERISTIC_OTA_UUID);
//                mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_OTA_DESCRIPTOR_OTA_UUID);

                // SPP协议配置（当前使用的配置）
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);  // 设置使用SPP协议
                mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);  // 设置服务UUID
                mServiceConfig.setUseTotaV2(false);     // 不使用TotaV2协议
                mServiceConfig.setTotaConnect(false);   // 不使用Tota连接
                mServiceConfig.setUSER_FLAG(1);         // 设置用户标志
                mServiceConfig.setCurUser(1);           // 设置当前用户
                mServiceConfig.setDevice(mHmDevice);    // 设置目标设备

                // 创建BES OTA服务实例并启动
                BesOtaService besOtaService = new BesOtaService(mServiceConfig, DemoActivity.this, DemoActivity.this);
                otaTask = besOtaService;  // 保存OTA任务引用
            }
        });

        // OTA升级取消按钮点击事件监听器
        // 点击后可以中断正在进行的OTA升级过程
        findViewById(R.id.tvOtaUpdateCancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 中断升级操作（当前已注释，如需使用请取消注释）
                // 该方法会中断当前的OTA升级过程并释放相关资源
                //BesOtaManager.getInstance().interruptUpgrade();
            }
        });

    }

    /**
     * 处理从其他Activity返回的结果
     *
     * 主要用于处理设备扫描Activity返回的设备选择结果
     *
     * @param requestCode 请求码，用于识别是哪个Activity的返回结果
     * @param resultCode 结果码，表示操作是否成功
     * @param data 返回的数据Intent，包含选中的设备信息
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        // 检查是否为设备扫描Activity的返回结果
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    /**
     * 处理设备选择结果
     *
     * 当用户从设备扫描列表中选择了一个设备后，此方法会被调用
     * 主要功能：
     * 1. 从Intent中提取选中的设备信息
     * 2. 根据设备支持的协议获取对应的蓝牙设备对象
     * 3. 在UI上显示选中设备的名称和地址
     * 4. 更新设备显示文本的颜色
     *
     * @param resultCode 操作结果码
     * @param data 包含设备信息的Intent数据
     */
    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            // 从Intent中获取选中的HmDevice对象
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);

            // 根据设备首选协议获取对应的蓝牙设备对象
            // 如果是BLE协议则使用BLE地址，否则使用经典蓝牙MAC地址
            mDevice = BtHeleper.getBluetoothAdapter(this).getRemoteDevice(
                mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ?
                mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            // 在UI上显示设备名称和地址
            tvDevice.setText(mDevice.getName() + "   " + mDevice.getAddress());
            // 设置文本颜色为禁用状态颜色（表示已选中）
            tvDevice.setTextColor(getColor(R.color.btnDisableColor));
        }
    }

    /**
     * 处理返回按键事件
     *
     * 当用户按下返回键时，结束当前Activity
     */
    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();  // 结束当前Activity
    }

    /**
     * BesServiceListener回调方法：Tota连接状态变化
     *
     * 当Tota连接状态发生变化时会调用此方法
     *
     * @param state 连接状态，true表示已连接，false表示已断开
     * @param hmDevice 相关的设备对象
     */
    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        // 此方法当前为空实现，可根据需要添加连接状态处理逻辑
    }

    /**
     * BesServiceListener回调方法：错误消息处理
     *
     * 当OTA过程中发生错误时会调用此方法
     *
     * @param msg 错误消息码
     * @param hmDevice 相关的设备对象
     */
    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        // 此方法当前为空实现，可根据需要添加错误处理逻辑
    }

    /**
     * BesServiceListener回调方法：状态变化消息处理
     *
     * 当OTA升级过程中状态发生变化时会调用此方法
     * 这是OTA升级流程的核心回调方法
     *
     * @param msg 状态消息码
     * @param msgStr 状态消息字符串
     * @param hmDevice 相关的设备对象
     */
    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        // 在UI线程中处理状态变化
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                switch (msg) {
                    case OTA_CMD_GET_HW_INFO:  // 获取硬件信息成功，可以开始数据传输
                        // ⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️ 详细实现请参考OtaUIActivity

                        // 创建远程OTA配置对象
                        RemoteOTAConfig config = new RemoteOTAConfig();
                        // 设置本地升级文件路径
                        config.setLocalPath(tvFileSelect.getText().toString());
                        // 将配置应用到OTA任务
                        otaTask.setOtaConfig(config);

                        // 创建OTA DFU信息对象
                        // 参数说明：设备ID为"001"，断点参数为0
                        // breakpoint为0表示从头开始升级，为1表示从断点处继续升级
                        OTADfuInfo otaDfuInfo = new OTADfuInfo("001", 0);

                        // 开始数据传输（正式开始OTA升级过程）
                        otaTask.startDataTransfer(otaDfuInfo, DemoActivity.this);
                        break;
                    default:
                        // 其他状态消息暂不处理
                        break;
                }
            }
        });
    }

    /**
     * BesServiceListener回调方法：成功消息处理
     *
     * 当OTA过程中某个步骤成功完成时会调用此方法
     *
     * @param msg 成功消息码
     * @param hmDevice 相关的设备对象
     */
    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {
        // 此方法当前为空实现，可根据需要添加成功处理逻辑
    }

    /**
     * OTATask.StatusListener回调方法：OTA状态变化
     *
     * 当OTA升级状态发生变化时会调用此方法
     *
     * @param newStatus 新的OTA状态
     * @param hmDevice 相关的设备对象
     */
    @Override
    public void onOTAStatusChanged(OTAStatus newStatus, HmDevice hmDevice) {
        // 此方法当前为空实现，可根据需要添加状态变化处理逻辑
    }

    /**
     * OTATask.StatusListener回调方法：OTA进度变化
     *
     * 当OTA升级进度发生变化时会调用此方法
     * 这是监控升级进度的主要方法
     *
     * @param progress 升级进度，范围通常为0.0到100.0
     * @param hmDevice 相关的设备对象
     */
    @Override
    public void onOTAProgressChanged(float progress, HmDevice hmDevice) {
        // 在UI线程中更新进度显示
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // 在日志文本视图中显示当前升级进度
                tvLog.setText("progress-->" + progress);
            }
        });
    }
}
