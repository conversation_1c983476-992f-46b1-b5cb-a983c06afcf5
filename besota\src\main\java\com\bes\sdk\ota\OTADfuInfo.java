package com.bes.sdk.ota;

/**
 * OTA DFU信息类
 *
 * 该类用于封装OTA（Over-The-Air）升级过程中的DFU（Device Firmware Update）相关信息。
 * DFU是设备固件更新的标准协议，用于管理固件升级过程中的版本控制和断点续传功能。
 *
 * 主要功能：
 * 1. 存储DFU版本信息
 * 2. 管理断点续传位置
 * 3. 支持升级过程的状态恢复
 * 4. 提供升级参数的封装
 *
 * 使用场景：
 * - OTA固件升级初始化
 * - 断点续传功能实现
 * - 升级进度管理
 * - 升级状态恢复
 *
 * 断点续传说明：
 * - breakpoint = 0: 从头开始全新升级
 * - breakpoint > 0: 从指定位置继续升级（已传输的数据长度）
 *
 * 使用示例：
 * ```java
 * // 全新升级
 * OTADfuInfo dfuInfo = new OTADfuInfo("001", 0);
 *
 * // 断点续传
 * OTADfuInfo dfuInfo = new OTADfuInfo("001", 1024);
 * ```
 */
public class OTADfuInfo
{
    /** DFU版本号，用于标识固件版本信息 */
    // OTA DFU version.
    private String mVer;

    /** DFU断点位置，表示已传输到设备的数据长度（字节数） */
    // OTA DFU breakpoint.
    private int mBp;

    /**
     * 构造OTA DFU信息对象
     *
     * 创建一个包含版本信息和断点位置的DFU信息对象。该对象用于配置
     * OTA升级过程中的关键参数。
     *
     * @param version 固件版本号，通常是字符串格式的版本标识
     * @param breakpoint 断点位置，表示已传输的数据长度
     *                   - 0: 从头开始全新升级
     *                   - >0: 从指定字节位置继续传输（断点续传）
     */
    public OTADfuInfo(String version, int breakpoint)
    {
        mVer = version;
        mBp = breakpoint;
    }

    /**
     * 获取DFU版本号
     *
     * 获取当前DFU的版本标识。版本号用于标识固件的具体版本，
     * 帮助系统确认升级的目标版本和兼容性。
     *
     * Get the DFU version, can be null if there is no DFU available.
     *
     * @return DFU版本号字符串，如果没有可用的DFU则可能为null
     */
    public String getVersion() { return mVer; };

    /**
     * 获取DFU断点位置
     *
     * 获取已传输到设备的数据长度（以字节为单位）。该值用于实现断点续传功能，
     * 当升级过程中断时，可以从此位置继续传输，避免重新开始。
     *
     * 返回值说明：
     * - 0: 表示尚未开始传输或需要从头开始
     * - >0: 表示已传输的字节数，可以从此位置继续
     *
     * Get DFU breakpoint, the data length that already transferred to device.
     *
     * @return 已传输的数据长度（字节数）
     */
    public int getBreakpoint() { return mBp; }

    /**
     * 返回对象的字符串表示
     *
     * 生成包含版本号和断点位置的格式化字符串，主要用于调试和日志输出。
     * 输出格式便于开发者查看当前DFU信息的详细状态。
     *
     * @return 包含版本号和断点位置的格式化字符串
     */
    @Override
    public String toString() {
        return "OTADfuInfo{" +
                "\nmVer='" + mVer + '\'' +
                "\nmBp=" + mBp +
                '}';
    }
}
