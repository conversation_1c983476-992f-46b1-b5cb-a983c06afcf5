# BES OTA Android 项目文档

## 项目概述

BesOTA_android 是一个专为BES芯片设计的Android OTA（Over-The-Air）固件升级应用。该项目提供了完整的蓝牙设备管理、固件升级、设备调试等功能，支持SPP和BLE两种蓝牙协议。

### 核心功能
- 🔗 蓝牙设备扫描和连接管理
- 📱 OTA固件升级（支持SPP/BLE协议）
- 🛠️ 设备调试工具集
- 🎵 音频处理和EQ调节
- 📊 设备状态监控和日志记录
- 🎨 自定义表盘制作
- 🔍 设备查找功能

### 技术架构
- **架构模式**: MVP (Model-View-Presenter)
- **开发语言**: Java + Kotlin
- **最低SDK版本**: API 23 (Android 6.0)
- **目标SDK版本**: API 29 (Android 10)
- **编译SDK版本**: API 33 (Android 13)

## DemoActivity 详细说明

### 类概述
`DemoActivity` 是BES OTA升级功能的演示Activity，位于 `com.besall.allbase.view.activity.chipstoollevel4.ota` 包中。该Activity提供了一个简化的OTA升级界面，展示了完整的OTA升级流程。

### 主要功能
1. **设备选择**: 通过扫描Activity选择目标蓝牙设备
2. **文件配置**: 设置OTA升级文件路径
3. **连接建立**: 配置并建立与目标设备的连接
4. **升级执行**: 启动并监控OTA升级过程
5. **进度监控**: 实时显示升级进度和状态

### 核心组件

#### 成员变量
```java
BluetoothDevice mDevice;           // 选中的蓝牙设备对象
HmDevice mHmDevice;                // HM设备对象，包含设备详细信息
OTATask otaTask;                   // OTA升级任务对象
BesServiceConfig mServiceConfig;   // BES服务配置对象
```

#### UI控件
```java
View btDeviceSelect;    // 设备选择按钮
TextView tvDevice;      // 设备信息显示
TextView tvFileSelect;  // 文件路径显示
View tvOtaUpdate;       // OTA升级开始按钮
TextView tvState;       // 状态显示
TextView tvLog;         // 日志和进度显示
```

### 工作流程

#### 1. 设备选择流程
```
用户点击设备选择按钮 → 启动ScanActivity → 用户选择设备 → 返回设备信息 → 显示设备名称和地址
```

#### 2. OTA升级流程
```
用户点击升级按钮 → 初始化BesServiceConfig → 配置连接参数 → 创建BesOtaService → 等待连接成功 → 开始数据传输 → 监控升级进度
```

### 协议支持

#### SPP协议配置（当前使用）
```java
mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
mServiceConfig.setUseTotaV2(false);
mServiceConfig.setTotaConnect(false);
```

#### BLE协议配置（可选）
```java
mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_OTA_CHARACTERISTIC_OTA_UUID);
mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_OTA_DESCRIPTOR_OTA_UUID);
```

### 回调接口

#### BesServiceListener 回调
- `onTotaConnectState()`: Tota连接状态变化
- `onErrorMessage()`: 错误消息处理
- `onStateChangedMessage()`: 状态变化消息（核心回调）
- `onSuccessMessage()`: 成功消息处理

#### OTATask.StatusListener 回调
- `onOTAStatusChanged()`: OTA状态变化
- `onOTAProgressChanged()`: OTA进度变化（实时更新）

### 使用注意事项

1. **文件路径配置**: 需要修改代码中的默认文件路径为实际的升级文件路径
2. **权限要求**: 需要蓝牙、存储等相关权限
3. **协议选择**: 根据目标设备支持的协议选择SPP或BLE
4. **断点续传**: 支持断点续传功能，通过OTADfuInfo的第二个参数控制

### 相关文件
- 布局文件: `app/src/main/res/layout/activity_demo.xml`
- 完整OTA实现: `OtaUIActivity.java`（参考完整功能实现）

## 项目文件结构

### 根目录结构
```
BesOTA_android/
├── app/                          # 主应用模块
├── besota/                       # BES OTA SDK模块
├── build.gradle                  # 项目级构建配置
├── settings.gradle               # 项目设置
├── gradle.properties             # Gradle属性配置
├── local.properties              # 本地属性配置
├── login-with-amazon-sdk.jar     # Amazon登录SDK
└── gradlew                       # Gradle包装器
```

### 主应用模块 (app/)
```
app/
├── src/main/
│   ├── java/com/besall/allbase/
│   │   ├── app/                  # 应用程序入口
│   │   ├── bluetooth/            # 蓝牙相关功能
│   │   ├── common/               # 通用工具类
│   │   └── view/                 # UI层
│   │       ├── activity/         # Activity集合
│   │       │   ├── chipstoollevel4/  # 芯片工具功能
│   │       │   │   ├── ota/      # OTA升级功能
│   │       │   │   │   ├── DemoActivity.java
│   │       │   │   │   ├── OtaUIActivity.java
│   │       │   │   │   └── ...
│   │       │   │   ├── checkcrc/ # CRC校验功能
│   │       │   │   ├── commandset/ # 命令集功能
│   │       │   │   ├── customercmd/ # 自定义命令
│   │       │   │   ├── customerdial/ # 自定义表盘
│   │       │   │   └── findmy/   # 查找设备功能
│   │       │   ├── level2/       # 二级功能
│   │       │   ├── level3/       # 三级功能
│   │       │   └── tools/        # 工具集
│   │       └── base/             # 基础类
│   ├── res/                      # 资源文件
│   │   ├── layout/               # 布局文件
│   │   ├── drawable/             # 图片资源
│   │   ├── values/               # 值资源
│   │   └── ...
│   └── AndroidManifest.xml       # 应用清单
├── build.gradle                  # 模块构建配置
└── proguard-rules.pro           # 混淆规则
```

### BES OTA SDK模块 (besota/)
```
besota/
├── src/main/
│   ├── java/                     # SDK源码
│   ├── jniLibs/                  # 原生库文件
│   └── AndroidManifest.xml      # SDK清单
├── build.gradle                  # SDK构建配置
└── proguard-rules.pro           # SDK混淆规则
```

## 主要功能模块详解

### 1. OTA升级模块 (ota/)
**位置**: `app/src/main/java/com/besall/allbase/view/activity/chipstoollevel4/ota/`

**核心文件**:
- `DemoActivity.java` - OTA升级演示界面
- `OtaUIActivity.java` - 完整OTA升级界面
- `OtaUIPresenter.java` - OTA业务逻辑处理
- `OtaConfigFragment.java` - OTA配置片段

**功能特性**:
- 支持SPP和BLE两种协议
- 断点续传功能
- 实时进度监控
- 多文件升级支持
- 升级日志记录

### 2. 蓝牙管理模块 (bluetooth/)
**位置**: `app/src/main/java/com/besall/allbase/bluetooth/`

**主要功能**:
- 设备扫描和发现
- 连接状态管理
- 协议切换支持
- 设备信息获取

### 3. 设备调试工具 (chipstoollevel4/)
**位置**: `app/src/main/java/com/besall/allbase/view/activity/chipstoollevel4/`

**子模块**:
- **checkcrc/**: CRC校验工具
- **commandset/**: 设备命令集
- **customercmd/**: 自定义命令工具
- **customerdial/**: 自定义表盘制作
- **findmy/**: 设备查找功能

### 4. 音频处理模块
**功能**:
- EQ均衡器调节
- 音频转储功能
- 音频播放测试
- 音频参数配置

### 5. 系统工具模块
**功能**:
- 日志转储
- 崩溃日志收集
- RSSI信号强度监测
- 吞吐量测试

## 技术栈和依赖

### 核心依赖
```gradle
// Android支持库
implementation 'androidx.appcompat:appcompat:1.2.0'
implementation 'com.google.android.material:material:1.2.1'
implementation 'androidx.constraintlayout:constraintlayout:2.0.2'

// 工具库
api 'com.blankj:utilcodex:1.30.6'
implementation 'com.google.code.gson:gson:2.8.6'

// 响应式编程
implementation 'io.reactivex.rxjava2:rxjava:2.0.5'
implementation 'com.github.tbruyelle:rxpermissions:0.10.2'

// UI组件
implementation 'com.nbsp:materialfilepicker:1.9.1'
implementation 'com.github.zcweng:switch-button:0.0.3@aar'

// 内存泄漏检测
debugImplementation 'com.squareup.leakcanary:leakcanary-android:1.6.3'
```

### 开发工具
- **IDE**: Android Studio
- **构建工具**: Gradle 4.1.0
- **Kotlin版本**: 1.6.21
- **NDK支持**: arm64-v8a架构

## 使用指南

### 环境要求
- Android Studio 4.0+
- JDK 8+
- Android SDK API 23+
- 支持蓝牙的Android设备

### 编译步骤
1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 同步Gradle依赖
4. 连接Android设备或启动模拟器
5. 运行应用

### 配置说明

#### 1. 修改OTA文件路径
在`DemoActivity.java`中修改默认文件路径：
```java
// 修改为实际的升级文件路径
tvFileSelect.setText("/your/actual/path/to/ota/file.bin");
```

#### 2. 权限配置
确保在`AndroidManifest.xml`中添加必要权限：
```xml
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

#### 3. 协议选择
根据目标设备选择合适的协议：
- **SPP协议**: 适用于经典蓝牙设备
- **BLE协议**: 适用于低功耗蓝牙设备

### 常见问题

#### Q: OTA升级失败怎么办？
A: 检查以下几点：
1. 确认升级文件路径正确
2. 确认设备连接状态
3. 检查设备是否支持选择的协议
4. 查看日志输出获取详细错误信息

#### Q: 如何切换蓝牙协议？
A: 在`DemoActivity`中注释/取消注释相应的协议配置代码。

#### Q: 支持哪些文件格式？
A: 主要支持`.bin`格式的固件文件，具体格式需要符合BES芯片要求。

## 项目维护

### 版本信息
- **当前版本**: 20230510.01
- **版本代码**: 27
- **最后更新**: 2023年6月21日

### 开发团队
- **主要开发者**: fanyu
- **项目类型**: BES芯片OTA升级工具

### 许可证
本项目为BES公司内部开发工具，请遵循相关使用协议。

---

*本文档最后更新时间: 2024年12月30日*
